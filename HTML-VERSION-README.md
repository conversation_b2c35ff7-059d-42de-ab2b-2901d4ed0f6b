# 车载空气质量监测 HTML 版本

## 概述

这是一个将原始 React 项目完全转换为纯 HTML 的版本，保持了与原项目完全一致的 UI 效果。

## 文件结构

```
├── air-quality-monitor.html    # 主 HTML 文件
├── assets/                     # 资源文件夹
│   └── images/                 # 图片资源
│       ├── status-bar.svg      # 状态栏
│       ├── background.png      # 背景图片
│       ├── ellipse-11.svg      # 外圆渐变
│       ├── ellipse-12.svg      # 中圆渐变
│       ├── ellipse-13.svg      # 内圆渐变
│       ├── car.png             # 车辆可视化
│       ├── group-13.png        # 空气质量标志
│       ├── mask-group.png      # 遮罩组
│       ├── frame-1.svg         # 天气和空气质量信息卡片
│       └── frame-2.svg         # 控制面板
└── HTML-VERSION-README.md      # 本说明文件
```

## 功能特性

### ✅ 完全一致的 UI
- 与原 React 版本像素级一致
- 所有图片资源已本地化
- 保持原有的 iPhone 尺寸 (393×852px)

### ✅ 独立运行
- 无需 Node.js 或任何构建工具
- 无需网络连接（所有资源已本地化）
- 可直接在任何现代浏览器中打开

### ✅ 完整的样式
- 内嵌完整的 Tailwind CSS 样式
- 保持原有的 HarmonyOS Sans 字体
- 所有颜色、间距、动画效果完全一致

## 使用方法

### 方法一：直接打开
双击 `air-quality-monitor.html` 文件，或在浏览器中打开该文件。

### 方法二：本地服务器
如果需要通过 HTTP 协议访问（推荐），可以使用以下任一方法：

#### 使用 Python
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

#### 使用 Node.js
```bash
npx serve .
```

#### 使用 PHP
```bash
php -S localhost:8000
```

然后在浏览器中访问 `http://localhost:8000/air-quality-monitor.html`

## 显示的数据

界面显示以下车载空气质量监测数据：

- **综合空气质量评级**: 25分（空气优）
- **PM2.5**: 014 µg/m³
- **甲醛**: 36 µg/m³  
- **负氧离子**: 25500 个/cm³
- **健康守护天数**: 231 天
- **清洗提醒设置**: 可点击设置

## 技术细节

### CSS 框架
- 使用完整的 Tailwind CSS 样式系统
- 所有样式内嵌在 HTML 文件中
- 支持响应式设计和现代 CSS 特性

### 图片资源
- 所有图片从原始 CDN 下载并本地化
- 支持 SVG 和 PNG 格式
- 保持原始分辨率和质量

### 兼容性
- 支持所有现代浏览器（Chrome、Firefox、Safari、Edge）
- 移动端友好
- 无 JavaScript 依赖

## 与原版本的差异

### 相同点
- ✅ 完全一致的视觉效果
- ✅ 相同的布局和尺寸
- ✅ 相同的字体和颜色
- ✅ 相同的图片和图标

### 差异点
- ❌ 无交互功能（原版本也主要是静态展示）
- ❌ 无 React 组件化架构
- ❌ 无路由功能（原版本只有单页面）

## 部署建议

### 静态网站托管
可以直接部署到任何静态网站托管服务：
- GitHub Pages
- Netlify
- Vercel
- AWS S3
- 阿里云 OSS

### 嵌入其他项目
可以作为 iframe 嵌入到其他网站中：
```html
<iframe src="air-quality-monitor.html" width="393" height="852" frameborder="0"></iframe>
```

## 文件大小

- HTML 文件: ~15KB（包含所有 CSS）
- 图片资源总计: ~3.6MB
- 总体积: ~3.6MB

## 许可证

与原项目保持一致的许可证。
