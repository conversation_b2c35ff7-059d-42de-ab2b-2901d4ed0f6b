<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车载空气质量监测 - 正确版本</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .monitor-container {
            width: 400px;
            height: 500px;
            background: #f8f9fa;
            border-radius: 24px;
            padding: 40px 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
            text-align: center;
        }

        /* 顶部添加设备按钮 */
        .add-device-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 60px;
            height: 30px;
            background: #00a67c;
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 半圆弧区域 */
        .arc-container {
            position: relative;
            width: 100%;
            height: 250px;
            margin: 20px 0;
        }

        /* 椭圆背景 */
        .ellipse-background {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            width: 280px;
            height: 160px;
            background: radial-gradient(ellipse at center, rgba(178, 230, 235, 0.3) 0%, rgba(178, 230, 235, 0.1) 70%, transparent 100%);
            border-radius: 50%;
            z-index: 1;
        }

        .arc-svg {
            position: relative;
            width: 100%;
            height: 200px;
            z-index: 2;
        }

        /* 中心文字内容 */
        .center-content {
            position: absolute;
            top: 50%;
            left: 35%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 3;
        }

        .air-quality-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .air-quality-status {
            color: #333;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .air-quality-score {
            color: #00bcd4;
            font-size: 64px;
            font-weight: 300;
            line-height: 1;
        }

        /* 车辆图片 - 紧贴数字25右侧 */
        .car-container {
            position: absolute;
            top: 110px;
            left: 55%;
            width: 160px;
            height: 90px;
            z-index: 4;
        }

        .car-image {
            width: 100%;
            height: auto;
            filter: drop-shadow(0 4px 12px rgba(0,0,0,0.15));
        }

        /* 底部信息 */
        .bottom-info {
            margin-top: 30px;
            text-align: center;
        }

        .protection-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .protection-days {
            color: #333;
            font-weight: 600;
        }

        .settings-link {
            color: #ff6b35;
            font-size: 14px;
            text-decoration: none;
        }

        /* 数据指标 */
        .metrics-container {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            padding: 0 10px;
        }

        .metric-item {
            text-align: center;
            flex: 1;
        }

        .metric-value {
            color: #333;
            font-size: 24px;
            font-weight: 600;
            line-height: 1;
        }

        .metric-label {
            color: #999;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="monitor-container">
        <!-- 添加设备按钮 -->
        <button class="add-device-btn">添加设备</button>

        <!-- 半圆弧区域 -->
        <div class="arc-container">
            <!-- 椭圆背景 -->
            <div class="ellipse-background"></div>

            <svg class="arc-svg" viewBox="0 0 400 200">
                <defs>
                    <!-- 彩虹渐变 -->
                    <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#ff4757"/>
                        <stop offset="25%" style="stop-color:#ff6b35"/>
                        <stop offset="50%" style="stop-color:#ffa502"/>
                        <stop offset="75%" style="stop-color:#7bed9f"/>
                        <stop offset="100%" style="stop-color:#70a1ff"/>
                    </linearGradient>
                </defs>

                <!-- 标准180度半圆弧 -->
                <path d="M 50 150 A 150 150 0 0 1 350 150"
                      fill="none"
                      stroke="url(#arcGradient)"
                      stroke-width="6"
                      stroke-linecap="round"/>
            </svg>

            <!-- 中心文字内容 -->
            <div class="center-content">
                <div class="air-quality-label">车内综合空气质量</div>
                <div class="air-quality-status">空气优</div>
                <div class="air-quality-score">25</div>
            </div>

            <!-- 车辆图片 - 在椭圆内部 -->
            <div class="car-container">
                <svg class="car-image" viewBox="0 0 240 100" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <!-- 车身渐变 -->
                        <linearGradient id="carBodyGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#b8e6e1"/>
                            <stop offset="50%" style="stop-color:#81d4d4"/>
                            <stop offset="100%" style="stop-color:#4ecdc4"/>
                        </linearGradient>
                        <!-- 车窗渐变 -->
                        <linearGradient id="windowGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:rgba(255,255,255,0.6)"/>
                            <stop offset="100%" style="stop-color:rgba(255,255,255,0.2)"/>
                        </linearGradient>
                    </defs>

                    <!-- 车身主体 - 现代SUV造型 -->
                    <path d="M 30 60 Q 30 45 50 40 L 190 40 Q 210 45 210 60 L 210 70 Q 210 75 205 75 L 35 75 Q 30 75 30 70 Z"
                          fill="url(#carBodyGradient)" stroke="rgba(0,0,0,0.1)" stroke-width="1"/>

                    <!-- 前挡风玻璃 -->
                    <path d="M 50 40 Q 70 35 90 35 L 150 35 Q 170 35 190 40 L 190 50 Q 170 45 150 45 L 90 45 Q 70 45 50 50 Z"
                          fill="url(#windowGradient)"/>

                    <!-- 侧窗 -->
                    <ellipse cx="120" cy="50" rx="50" ry="8" fill="url(#windowGradient)"/>

                    <!-- 前轮 -->
                    <circle cx="70" cy="75" r="15" fill="#2c3e50"/>
                    <circle cx="70" cy="75" r="10" fill="#34495e"/>
                    <circle cx="70" cy="75" r="6" fill="#7f8c8d"/>

                    <!-- 后轮 -->
                    <circle cx="170" cy="75" r="15" fill="#2c3e50"/>
                    <circle cx="170" cy="75" r="10" fill="#34495e"/>
                    <circle cx="170" cy="75" r="6" fill="#7f8c8d"/>

                    <!-- 车灯 -->
                    <ellipse cx="45" cy="55" rx="8" ry="4" fill="rgba(255,255,255,0.8)"/>
                    <ellipse cx="195" cy="55" rx="6" ry="3" fill="rgba(255,100,100,0.6)"/>

                    <!-- 车身高光 -->
                    <path d="M 50 45 Q 120 42 190 45" stroke="rgba(255,255,255,0.4)" stroke-width="2" fill="none"/>
                </svg>
            </div>
        </div>



        <!-- 底部信息 -->
        <div class="bottom-info">
            <div class="protection-info">
                已为您健康守护 <span class="protection-days">231</span> 天
                <a href="#" class="settings-link">设置 清洗提醒</a>
            </div>
        </div>

        <!-- 数据指标 -->
        <div class="metrics-container">
            <div class="metric-item">
                <div class="metric-value">014</div>
                <div class="metric-label">PM2.5(μg/m³)</div>
            </div>
            <div class="metric-item">
                <div class="metric-value">36</div>
                <div class="metric-label">甲醛(μg/m³)</div>
            </div>
            <div class="metric-item">
                <div class="metric-value">25500</div>
                <div class="metric-label">负氧离子(个/cm³)</div>
            </div>
        </div>
    </div>
</body>
</html>
