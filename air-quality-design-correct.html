<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车载空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
            background: #f6f6f6;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: #f6f6f6;
            position: relative;
            overflow: hidden;
            border-radius: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #000;
            font-size: 17px;
            font-weight: 600;
            z-index: 100;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 16px;
        }

        /* 添加设备按钮 */
        .add-device-btn {
            position: absolute;
            top: 75px;
            right: 20px;
            width: 52px;
            height: 52px;
            background: #2E8B7A;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 11px;
            font-weight: 500;
            text-align: center;
            z-index: 10;
            cursor: pointer;
            line-height: 1.1;
            box-shadow: 0 4px 12px rgba(46, 139, 122, 0.3);
        }

        .main-content {
            position: absolute;
            top: 44px;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 0 20px;
        }

        /* 仪表盘区域 - 重新设计为完整半圆 */
        .gauge-section {
            position: relative;
            width: 100%;
            height: 340px;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 40px;
        }

        .gauge-container {
            position: relative;
            width: 320px;
            height: 160px;
        }

        .gauge-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        /* 中央内容区域 - 在圆弧上方中央 */
        .center-content {
            position: absolute;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            z-index: 10;
        }

        .air-quality-subtitle {
            color: #999;
            font-size: 12px;
            margin-bottom: 3px;
            font-weight: 400;
        }

        .air-quality-title {
            color: #333;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .air-quality-score {
            color: #2E8B7A;
            font-size: 64px;
            font-weight: 300;
            line-height: 1;
            margin: 0;
        }

        /* 车辆图片 - 在圆弧中央穿过 */
        .car-image-container {
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 100px;
            z-index: 5;
        }

        .car-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* 保护天数信息 */
        .protection-info {
            text-align: center;
            margin: 25px 0 15px;
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }

        .cleaning-reminder {
            color: #FF9500;
            cursor: pointer;
            text-decoration: none;
        }

        /* 指标容器 */
        .metrics-container {
            display: flex;
            justify-content: space-between;
            margin: 25px 0 30px;
            padding: 0 10px;
        }

        .metric-item {
            text-align: center;
            flex: 1;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 400;
            color: #333;
            line-height: 1;
            margin-bottom: 8px;
        }

        .metric-label {
            font-size: 11px;
            color: #999;
            font-weight: 400;
        }

        /* 天气卡片 */
        .weather-card {
            background: white;
            border-radius: 16px;
            padding: 18px 20px;
            margin: 0 0 16px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .weather-left {
            flex: 1;
        }

        .weather-location {
            font-size: 20px;
            font-weight: 600;
            color: #1A1A1A;
            margin-bottom: 2px;
        }

        .weather-date {
            font-size: 10px;
            color: #999;
        }

        .weather-temp {
            font-size: 48px;
            font-weight: 300;
            color: #333;
            line-height: 1;
        }

        .weather-info {
            font-size: 11px;
            color: #666;
            line-height: 1.4;
            margin-bottom: 12px;
        }

        .weather-details {
            display: flex;
            justify-content: space-between;
        }

        .weather-item {
            text-align: center;
            flex: 1;
        }

        .weather-item-label {
            font-size: 11px;
            color: #666;
            margin-bottom: 6px;
        }

        .weather-value-outdoor {
            color: #FF9500;
            font-size: 24px;
            font-weight: 500;
        }

        .weather-value-indoor {
            color: #34C759;
            font-size: 24px;
            font-weight: 500;
        }

        .weather-value-anion {
            color: #34C759;
            font-size: 24px;
            font-weight: 500;
        }

        /* 控制面板 */
        .control-panel {
            background: white;
            border-radius: 16px;
            padding: 18px 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-item {
            text-align: center;
            flex: 1;
        }

        .control-title {
            font-size: 13px;
            color: #666;
            margin-bottom: 6px;
        }

        .control-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .power-button {
            width: 52px;
            height: 52px;
            background: #FF9500;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(255, 149, 0, 0.3);
        }

        .power-icon {
            color: white;
            font-size: 20px;
            font-weight: 300;
        }
        
        /* 刷新按钮 */
        .refresh-button {
            position: absolute;
            bottom: 24px;
            right: 24px;
            color: #ccc;
            font-size: 12px;
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .refresh-icon {
            margin-right: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">9:41</div>
            <div class="status-right">
                <span>●●●●</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>
        
        <!-- 添加设备按钮 -->
        <div class="add-device-btn">
            添加<br>设备
        </div>

        <div class="main-content">
            <!-- 仪表盘区域 -->
            <div class="gauge-section">
                <div class="gauge-container">
                    <!-- 完整半圆仪表盘 -->
                    <svg class="gauge-svg" viewBox="0 0 320 160">
                        <defs>
                            <!-- 仪表盘渐变 - 完整半圆 -->
                            <linearGradient id="gaugeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#00E5CC"/>
                                <stop offset="20%" style="stop-color:#66FF66"/>
                                <stop offset="40%" style="stop-color:#FFDD00"/>
                                <stop offset="70%" style="stop-color:#FF9500"/>
                                <stop offset="100%" style="stop-color:#FF3300"/>
                            </linearGradient>
                        </defs>
                        
                        <!-- 270度圆弧背景 - 从左下到右下，经过顶部 -->
                        <path d="M 70 140 A 100 100 0 1 1 250 140" 
                              stroke="#EEEEEE" 
                              stroke-width="12" 
                              fill="none" />
                              
                        <!-- 270度圆弧指示器 - 显示约25%的位置 -->
                        <path d="M 70 140 A 100 100 0 0 1 110 50" 
                              stroke="url(#gaugeGradient)" 
                              stroke-width="12" 
                              stroke-linecap="round"
                              fill="none" />
                    </svg>
                    
                    <!-- 中央内容 -->
                    <div class="center-content">
                        <div class="air-quality-subtitle">车内综合空气质量</div>
                        <div class="air-quality-title">空气优</div>
                        <div class="air-quality-score">25</div>
                    </div>
                    
                </div>
                
                <!-- 车辆图片 - 在圆弧中央 -->
                <div class="car-image-container">
                    <img class="car-image" src="assets/images/car.png" alt="电动汽车" />
                </div>
            </div>
            </div>

            <!-- 保护天数信息 -->
            <div class="protection-info">
                已为您健康守护 <span style="color: #333; font-weight: 500;">231</span> 天<br>
                设置 <a href="#" class="cleaning-reminder">清洗提醒</a>
            </div>

            <!-- 空气质量指标 -->
            <div class="metrics-container">
                <div class="metric-item">
                    <div class="metric-value">014</div>
                    <div class="metric-label">PM2.5(µg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">36</div>
                    <div class="metric-label">甲醛(µg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">25500</div>
                    <div class="metric-label">负氧离子(个/cm³)</div>
                </div>
            </div>

            <!-- 天气卡片 -->
            <div class="weather-card">
                <div class="weather-header">
                    <div class="weather-left">
                        <div class="weather-location">深圳市</div>
                        <div class="weather-date">2024-06-09 15:07 更新</div>
                    </div>
                    <div class="weather-temp">33°</div>
                </div>
                
                <div class="weather-info">
                    空气质量：中<br>
                    湿度：52%<br>
                    距离感：36km
                </div>
                
                <div class="weather-details">
                    <div class="weather-item">
                        <div class="weather-item-label">车外PM2.5</div>
                        <div class="weather-value-outdoor">105</div>
                    </div>
                    <div class="weather-item">
                        <div class="weather-item-label">车内PM2.5</div>
                        <div class="weather-value-indoor">14</div>
                    </div>
                    <div class="weather-item">
                        <div class="weather-item-label">车外负氧离子</div>
                        <div class="weather-value-anion">628</div>
                    </div>
                </div>
            </div>

            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="control-item">
                    <div class="control-title">设备运转</div>
                    <div class="control-value">正常</div>
                </div>
                <div class="control-item">
                    <div class="power-button">
                        <div class="power-icon">⏻</div>
                    </div>
                </div>
                <div class="control-item">
                    <div class="control-title">档位</div>
                    <div class="control-value">高</div>
                </div>
            </div>
            
            <!-- 刷新按钮 -->
            <div class="refresh-button">
                <span class="refresh-icon">⟳</span> 刷新
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 电源按钮点击
            const powerButton = document.querySelector('.power-button');
            if (powerButton) {
                powerButton.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                    console.log('电源控制已切换');
                });
            }
            
            // 清洗提醒设置
            const cleaningReminder = document.querySelector('.cleaning-reminder');
            if (cleaningReminder) {
                cleaningReminder.addEventListener('click', function(e) {
                    e.preventDefault();
                    alert('清洗提醒设置');
                });
            }
            
            // 添加设备
            const addDeviceBtn = document.querySelector('.add-device-btn');
            if (addDeviceBtn) {
                addDeviceBtn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                    console.log('添加新设备');
                });
            }
            
            // 刷新按钮
            const refreshButton = document.querySelector('.refresh-button');
            if (refreshButton) {
                refreshButton.addEventListener('click', function() {
                    console.log('数据已刷新');
                });
            }
        });
    </script>
</body>
</html>