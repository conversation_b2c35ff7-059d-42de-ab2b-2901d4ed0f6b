<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车载空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: #f5f5f5;
            position: relative;
            overflow: hidden;
            border-radius: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #000;
            font-size: 14px;
            font-weight: 600;
            z-index: 100;
        }

        .status-left {
            font-size: 14px;
            font-weight: 600;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .signal-icon, .wifi-icon, .battery-icon {
            display: inline-block;
        }

        /* 添加设备按钮 */
        .add-device-btn {
            position: absolute;
            top: 60px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #008D71;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 12px;
            text-align: center;
            z-index: 10;
            cursor: pointer;
            line-height: 1.2;
        }

        .main-content {
            position: absolute;
            top: 44px;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 20px;
        }

        /* 仪表盘区域 */
        .gauge-section {
            position: relative;
            width: 100%;
            height: 310px;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 20px;
        }

        .gauge-svg {
            position: absolute;
            top: 0;
            width: 310px;
            height: 155px;
        }

        /* 中央内容区域 */
        .center-content {
            position: relative;
            margin-top: 40px;
            text-align: center;
            z-index: 10;
        }

        .air-quality-subtitle {
            color: #666;
            font-size: 12px;
            margin-bottom: 5px;
            font-weight: 400;
        }

        .air-quality-title {
            color: #333;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .air-quality-score {
            color: #008D71;
            font-size: 70px;
            font-weight: 300;
            line-height: 1;
            margin: 0;
        }

        /* 车辆图片 */
        .car-image-container {
            width: 200px;
            height: 100px;
            margin: 20px auto 0;
            position: relative;
            z-index: 5;
        }

        .car-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* 保护天数信息 */
        .protection-info {
            text-align: center;
            margin: 20px 0;
            font-size: 14px;
            color: #666;
        }

        .cleaning-reminder {
            color: #ff8800;
            cursor: pointer;
            text-decoration: none;
            margin-left: 5px;
        }

        /* 指标容器 */
        .metrics-container {
            display: flex;
            justify-content: space-between;
            margin: 30px 0;
        }

        .metric-item {
            text-align: center;
            flex: 1;
        }

        .metric-value {
            font-size: 26px;
            font-weight: 500;
            color: #333;
            line-height: 1;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 12px;
            color: #999;
            font-weight: 400;
        }

        /* 天气卡片 */
        .weather-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .weather-location {
            font-size: 22px;
            font-weight: 600;
            color: #1A1A1A;
        }

        .weather-date {
            font-size: 10px;
            color: #999;
        }

        .weather-temp {
            font-size: 40px;
            font-weight: 400;
            color: #333;
        }

        .weather-details {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }

        .weather-info {
            font-size: 12px;
            color: #999;
            margin-bottom: 10px;
        }

        .weather-item {
            text-align: center;
            flex: 1;
        }

        .weather-item-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .weather-pm-indoor {
            color: #4CAF50;
            font-size: 24px;
            font-weight: 500;
        }

        .weather-pm-outdoor {
            color: #FF9800;
            font-size: 24px;
            font-weight: 500;
        }

        .weather-index {
            color: #4CAF50;
            font-size: 24px;
            font-weight: 500;
        }

        /* 控制面板 */
        .control-panel {
            background: white;
            border-radius: 16px;
            padding: 15px 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-item {
            text-align: center;
            flex: 1;
        }

        .device-status {
            text-align: center;
        }

        .status-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .status-value {
            font-size: 14px;
            color: #333;
        }

        .power-button {
            width: 50px;
            height: 50px;
            background: #FFBB64;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            cursor: pointer;
        }

        .power-icon {
            color: white;
            font-size: 24px;
        }
        
        .refresh-button {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: #bbb;
            font-size: 12px;
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .refresh-icon {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">9:41</div>
            <div class="status-right">
                <div class="signal-icon">●●●●</div>
                <div class="wifi-icon">📶</div>
                <div class="battery-icon">🔋</div>
            </div>
        </div>
        
        <!-- 添加设备按钮 -->
        <div class="add-device-btn">
            添加<br>设备
        </div>

        <div class="main-content">
            <!-- 仪表盘区域 -->
            <div class="gauge-section">
                <!-- 半圆仪表盘 -->
                <svg class="gauge-svg" viewBox="0 0 310 155">
                    <defs>
                        <!-- 仪表盘渐变 -->
                        <linearGradient id="gaugeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#00E5CC"/>
                            <stop offset="25%" style="stop-color:#8FE800"/>
                            <stop offset="50%" style="stop-color:#FFD600"/>
                            <stop offset="75%" style="stop-color:#FF8A00"/>
                            <stop offset="100%" style="stop-color:#FF3D00"/>
                        </linearGradient>
                    </defs>
                    
                    <!-- 半圆背景轨迹 -->
                    <path d="M 30 150 A 125 125 0 0 1 280 150" 
                          stroke="#EEEEEE" 
                          stroke-width="10" 
                          fill="none" />
                          
                    <!-- 半圆指示器 - 显示约30%的仪表盘值 -->
                    <path d="M 30 150 A 125 125 0 0 1 105 80" 
                          stroke="url(#gaugeGradient)" 
                          stroke-width="10" 
                          stroke-linecap="round"
                          fill="none" />
                </svg>
                
                <!-- 中央内容 -->
                <div class="center-content">
                    <div class="air-quality-subtitle">车内综合空气质量</div>
                    <div class="air-quality-title">空气优</div>
                    <div class="air-quality-score">25</div>
                </div>
                
                <!-- 车辆图片 -->
                <div class="car-image-container">
                    <img class="car-image" src="assets/images/car.png" alt="电动汽车" />
                </div>
            </div>

            <!-- 保护天数信息 -->
            <div class="protection-info">
                已为您健康守护 <span class="protection-days">231</span> 天
                <br>
                设置 <a href="#" class="cleaning-reminder">清洗提醒</a>
            </div>

            <!-- 空气质量指标 -->
            <div class="metrics-container">
                <div class="metric-item">
                    <div class="metric-value">014</div>
                    <div class="metric-label">PM2.5(µg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">36</div>
                    <div class="metric-label">甲醛(µg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">25500</div>
                    <div class="metric-label">负氧离子(个/cm³)</div>
                </div>
            </div>

            <!-- 天气卡片 -->
            <div class="weather-card">
                <div class="weather-header">
                    <div>
                        <div class="weather-location">深圳市</div>
                        <div class="weather-date">2024-06-09 15:07 更新</div>
                    </div>
                    <div class="weather-temp">33<sup>°C</sup></div>
                </div>
                
                <div class="weather-info">
                    空气质量：中<br>
                    湿度：52%<br>
                    相对湿度：36km
                </div>
                
                <div class="weather-details">
                    <div class="weather-item">
                        <div class="weather-item-label">车外PM2.5</div>
                        <div class="weather-pm-outdoor">105</div>
                    </div>
                    <div class="weather-item">
                        <div class="weather-item-label">车内PM2.5</div>
                        <div class="weather-pm-indoor">14</div>
                    </div>
                    <div class="weather-item">
                        <div class="weather-item-label">车外负氧离子</div>
                        <div class="weather-index">628</div>
                    </div>
                </div>
            </div>

            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="control-item device-status">
                    <div class="status-title">设备运转</div>
                    <div class="status-value">正常</div>
                </div>
                <div class="control-item">
                    <div class="power-button">
                        <div class="power-icon">⏻</div>
                    </div>
                </div>
                <div class="control-item device-status">
                    <div class="status-title">档位</div>
                    <div class="status-value">高</div>
                </div>
            </div>
            
            <!-- 刷新按钮 -->
            <div class="refresh-button">
                <span class="refresh-icon">⟳</span> 刷新
            </div>
        </div>
    </div>

    <script>
        // 可以添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 电源按钮点击
            const powerButton = document.querySelector('.power-button');
            if (powerButton) {
                powerButton.addEventListener('click', function() {
                    alert('电源控制已切换');
                });
            }
            
            // 清洗提醒设置
            const cleaningReminder = document.querySelector('.cleaning-reminder');
            if (cleaningReminder) {
                cleaningReminder.addEventListener('click', function(e) {
                    e.preventDefault();
                    alert('清洗提醒设置\n\n当前设置：每30天提醒一次\n上次清洗：15天前\n下次提醒：15天后\n\n建议定期清洗以保持最佳空气质量！');
                });
            }
            
            // 添加设备
            const addDeviceBtn = document.querySelector('.add-device-btn');
            if (addDeviceBtn) {
                addDeviceBtn.addEventListener('click', function() {
                    alert('添加新设备');
                });
            }
            
            // 刷新按钮
            const refreshButton = document.querySelector('.refresh-button');
            if (refreshButton) {
                refreshButton.addEventListener('click', function() {
                    alert('数据已刷新');
                });
            }
        });
    </script>
</body>
</html>