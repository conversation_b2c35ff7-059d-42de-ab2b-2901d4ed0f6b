<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车载空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* 响应式设计 */
        @media (max-width: 450px) {
            body {
                padding: 10px;
            }

            .phone-container {
                transform: scale(0.9);
            }
        }

        @media (max-width: 400px) {
            .phone-container {
                transform: scale(0.8);
            }
        }

        /* 可访问性改进 */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
            overflow: hidden;
            border-radius: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #000;
            font-size: 17px;
            font-weight: 600;
            z-index: 100;
        }

        .status-left {
            font-size: 17px;
            font-weight: 600;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
            align-items: flex-end;
        }

        .signal-bar {
            width: 3px;
            background: #000;
            border-radius: 1px;
        }

        .signal-bar:nth-child(1) { height: 4px; }
        .signal-bar:nth-child(2) { height: 6px; }
        .signal-bar:nth-child(3) { height: 8px; }
        .signal-bar:nth-child(4) { height: 10px; }

        .wifi-icon, .battery-icon {
            width: 15px;
            height: 10px;
            background: #000;
            border-radius: 2px;
            position: relative;
        }

        .wifi-icon::before {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            border: 2px solid #000;
            border-bottom: none;
            border-radius: 8px 8px 0 0;
            top: -2px;
            left: 1px;
        }

        .main-content {
            position: absolute;
            top: 44px;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 20px;
        }

        /* 彩色圆弧进度条 */
        .arc-container {
            position: relative;
            width: 280px;
            height: 280px;
            margin: 20px auto 0;
        }

        .arc-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .arc-progress {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .arc-progress circle {
            fill: none;
            stroke-width: 12;
            stroke-linecap: round;
        }

        .arc-bg {
            stroke: #e9ecef;
        }

        .arc-fill {
            stroke: url(#arcGradient);
            stroke-dasharray: 440;
            stroke-dashoffset: 440;
            animation: arcAnimation 2s ease-out forwards;
        }

        @keyframes arcAnimation {
            to {
                stroke-dashoffset: 220; /* 50% 的进度 */
            }
        }

        /* 中央内容区域 */
        .center-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
        }

        .air-quality-subtitle {
            color: #666;
            font-size: 12px;
            margin-bottom: 8px;
            font-weight: 400;
        }

        .air-quality-title {
            color: #00c896;
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .air-quality-score {
            color: #00c896;
            font-size: 72px;
            font-weight: 300;
            line-height: 1;
            margin-bottom: 20px;
        }

        /* 车辆图片 */
        .car-container {
            position: relative;
            margin: 20px 0;
        }

        .car-image {
            width: 200px;
            height: auto;
            filter: drop-shadow(0 10px 20px rgba(0,0,0,0.1));
        }

        /* 保护天数信息 */
        .protection-info {
            text-align: center;
            margin: 30px 0 20px;
            font-size: 14px;
            color: #666;
        }

        .protection-days {
            color: #333;
            font-weight: 600;
        }

        .cleaning-reminder {
            color: #ff6b35;
            cursor: pointer;
            text-decoration: none;
            margin-left: 10px;
        }

        .cleaning-reminder:hover {
            text-decoration: underline;
        }

        /* 指标容器 */
        .metrics-container {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 0 20px;
        }

        .metric-item {
            text-align: center;
            flex: 1;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 300;
            color: #333;
            line-height: 1;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 11px;
            color: #999;
            font-weight: 400;
        }

        /* 天气卡片 */
        .weather-card {
            background: rgba(255,255,255,0.9);
            border-radius: 16px;
            padding: 20px;
            margin: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .weather-location {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .weather-temp {
            font-size: 32px;
            font-weight: 300;
            color: #333;
        }

        .weather-details {
            display: flex;
            justify-content: space-between;
            text-align: center;
        }

        .weather-item {
            flex: 1;
        }

        .weather-value {
            display: block;
            font-size: 18px;
            font-weight: 600;
            color: #ff6b35;
            margin-bottom: 5px;
        }

        .weather-label {
            font-size: 12px;
            color: #666;
        }

        /* 控制面板 */
        .control-panel {
            background: rgba(255,255,255,0.9);
            border-radius: 16px;
            padding: 15px 20px;
            margin: 0 20px 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-item {
            text-align: center;
            flex: 1;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-item:hover {
            transform: translateY(-2px);
        }

        .control-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            transition: all 0.3s ease;
        }

        .control-item.active .control-icon {
            background: #ff6b35;
            color: white;
        }

        .control-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .control-item.active .control-label {
            color: #ff6b35;
        }

        /* 动态背景 */
        .dynamic-background {
            position: absolute;
            top: 164px;
            left: 0;
            width: 393px;
            height: 619px;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
            animation: backgroundShift 8s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% {
                background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
            }
            50% {
                background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 50%, #ce93d8 100%);
            }
        }

        /* 动态遮罩效果 */
        .dynamic-mask {
            position: absolute;
            top: 0;
            left: 22px;
            width: 349px;
            height: 349px;
            background: radial-gradient(circle at center,
                rgba(255,255,255,0.1) 0%,
                rgba(255,255,255,0.05) 40%,
                transparent 70%);
            animation: maskPulse 4s ease-in-out infinite;
        }

        @keyframes maskPulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.6;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
        }

        /* 天气卡片动态内容 */
        .weather-content {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .weather-location {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .weather-temp {
            font-size: 24px;
            color: #333;
            font-weight: bold;
        }

        .weather-condition {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }

        .weather-details {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            color: #666;
        }

        .weather-item {
            text-align: center;
        }

        .weather-value {
            font-weight: bold;
            color: #333;
            display: block;
            margin-bottom: 2px;
        }

        /* 控制面板动态内容 */
        .control-content {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border-radius: 12px;
            padding: 12px 15px;
            height: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-button {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .control-button:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-1px);
        }

        .control-button.active {
            background: #3498db;
            border-color: #3498db;
        }

        /* 数值动画 */
        .animated-value {
            transition: all 0.5s ease;
        }

        .metric-value.updating {
            color: #3498db;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">9:41</div>
            <div class="status-right">
                <div class="signal-bars">
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                </div>
                <div class="wifi-icon"></div>
                <div class="battery-icon"></div>
            </div>
        </div>

        <div class="main-content">
            <!-- 彩色圆弧进度条 -->
            <div class="arc-container">
                <svg class="arc-progress" viewBox="0 0 160 160">
                    <defs>
                        <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#00c896"/>
                            <stop offset="25%" style="stop-color:#ffd93d"/>
                            <stop offset="50%" style="stop-color:#ff8c42"/>
                            <stop offset="100%" style="stop-color:#ff6b6b"/>
                        </linearGradient>
                    </defs>
                    <circle class="arc-bg" cx="80" cy="80" r="70"/>
                    <circle class="arc-fill" cx="80" cy="80" r="70"/>
                </svg>

                <!-- 中央内容 -->
                <div class="center-content">
                    <div class="air-quality-subtitle">车内综合空气质量</div>
                    <div class="air-quality-title">空气优</div>
                    <div class="air-quality-score" id="air-score">25</div>

                    <!-- 车辆图片 -->
                    <div class="car-container">
                        <img class="car-image" src="assets/images/car.png" alt="车辆">
                    </div>
                </div>
            </div>

            <!-- 保护天数信息 -->
            <div class="protection-info">
                已为您健康守护 <span class="protection-days" id="protection-days">231</span> 天
                <a href="#" class="cleaning-reminder" onclick="openCleaningSettings()">设置 清洗提醒</a>
            </div>

            <!-- 空气质量指标 -->
            <div class="metrics-container">
                <div class="metric-item">
                    <div class="metric-value" id="pm25-value">014</div>
                    <div class="metric-label">PM2.5(µg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="formaldehyde-value">36</div>
                    <div class="metric-label">甲醛(µg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="anion-value">25500</div>
                    <div class="metric-label">负氧离子(个/cm³)</div>
                </div>
            </div>

            <!-- 天气卡片 -->
            <div class="weather-card">
                <div class="weather-header">
                    <div class="weather-location">深圳市</div>
                    <div class="weather-temp" id="temperature">33°C</div>
                </div>
                <div class="weather-details">
                    <div class="weather-item">
                        <span class="weather-value" id="pm25-outdoor">105</span>
                        <span class="weather-label">今外PM2.5</span>
                    </div>
                    <div class="weather-item">
                        <span class="weather-value" id="pm6-outdoor">14</span>
                        <span class="weather-label">今外PM6.5</span>
                    </div>
                    <div class="weather-item">
                        <span class="weather-value" id="weather-index">628</span>
                        <span class="weather-label">今外天气指数</span>
                    </div>
                </div>
            </div>

            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="control-item" onclick="toggleControl('equipment')">
                    <div class="control-icon">⚙️</div>
                    <div class="control-label">设备运转</div>
                </div>
                <div class="control-item active" onclick="toggleControl('power')">
                    <div class="control-icon">⏻</div>
                    <div class="control-label">开关</div>
                </div>
                <div class="control-item" onclick="toggleControl('level')">
                    <div class="control-icon">📊</div>
                    <div class="control-label">档位</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 空气质量数据
        const airQualityData = {
            score: 25,
            level: '空气优',
            pm25: 14,
            formaldehyde: 36,
            anion: 25500,
            protectionDays: 231
        };

        // 天气数据
        const weatherData = {
            temperature: 33,
            location: '深圳市',
            pm25Outdoor: 105,
            pm6Outdoor: 14,
            weatherIndex: 628
        };

        // 控制状态
        const controlStates = {
            equipment: false,
            power: true,
            level: false
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateAirQualityData();
            updateWeatherData();
            updateArcProgress();
            startDataSimulation();
        });

        // 更新空气质量数据
        function updateAirQualityData() {
            const scoreElement = document.getElementById('air-score');
            const pm25Element = document.getElementById('pm25-value');
            const formaldehydeElement = document.getElementById('formaldehyde-value');
            const anionElement = document.getElementById('anion-value');
            const daysElement = document.getElementById('protection-days');

            // 更新数值
            if (scoreElement) scoreElement.textContent = airQualityData.score;
            if (pm25Element) pm25Element.textContent = airQualityData.pm25.toString().padStart(3, '0');
            if (formaldehydeElement) formaldehydeElement.textContent = airQualityData.formaldehyde;
            if (anionElement) anionElement.textContent = airQualityData.anion;
            if (daysElement) daysElement.textContent = airQualityData.protectionDays;

            // 更新等级和颜色
            const titleElement = document.querySelector('.air-quality-title');
            if (titleElement) titleElement.textContent = airQualityData.level;
        }

        // 更新圆弧进度
        function updateArcProgress() {
            const arcFill = document.querySelector('.arc-fill');
            if (arcFill) {
                const progress = (100 - airQualityData.score) / 100; // 分数越低越好
                const circumference = 2 * Math.PI * 70; // 半径70
                const offset = circumference * (1 - progress * 0.75); // 最多显示75%的弧
                arcFill.style.strokeDashoffset = offset;
            }
        }

        // 更新天气数据
        function updateWeatherData() {
            const temperatureElement = document.getElementById('temperature');
            const pm25OutdoorElement = document.getElementById('pm25-outdoor');
            const pm6OutdoorElement = document.getElementById('pm6-outdoor');
            const weatherIndexElement = document.getElementById('weather-index');

            if (temperatureElement) temperatureElement.textContent = `${weatherData.temperature}°C`;
            if (pm25OutdoorElement) pm25OutdoorElement.textContent = weatherData.pm25Outdoor;
            if (pm6OutdoorElement) pm6OutdoorElement.textContent = weatherData.pm6Outdoor;
            if (weatherIndexElement) weatherIndexElement.textContent = weatherData.weatherIndex;
        }

        // 控制面板切换
        function toggleControl(controlId) {
            const controlItems = document.querySelectorAll('.control-item');

            // 移除所有active状态
            controlItems.forEach(item => {
                if (item.onclick.toString().includes(controlId)) {
                    // 切换当前项状态
                    controlStates[controlId] = !controlStates[controlId];

                    if (controlStates[controlId]) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                }
            });

            console.log(`${controlId} ${controlStates[controlId] ? '开启' : '关闭'}`);

            // 特殊效果
            if (controlId === 'power' && controlStates[controlId]) {
                // 开启时改善空气质量
                setTimeout(() => {
                    airQualityData.pm25 = Math.max(5, airQualityData.pm25 - 3);
                    airQualityData.formaldehyde = Math.max(20, airQualityData.formaldehyde - 5);
                    updateAirQualityData();
                    updateArcProgress();
                }, 1500);
            }
        }

        // 清洗提醒设置
        function openCleaningSettings() {
            alert('清洗提醒设置\n\n当前设置：每30天提醒一次\n上次清洗：15天前\n下次提醒：15天后\n\n建议定期清洗以保持最佳空气质量！');
        }

        // 数据模拟更新
        function startDataSimulation() {
            setInterval(() => {
                // 模拟车内空气质量数据变化
                airQualityData.pm25 += Math.floor(Math.random() * 3) - 1;
                airQualityData.formaldehyde += Math.floor(Math.random() * 3) - 1;
                airQualityData.anion += Math.floor(Math.random() * 200) - 100;

                // 确保数据在合理范围内
                airQualityData.pm25 = Math.max(5, Math.min(50, airQualityData.pm25));
                airQualityData.formaldehyde = Math.max(20, Math.min(80, airQualityData.formaldehyde));
                airQualityData.anion = Math.max(20000, Math.min(30000, airQualityData.anion));

                // 更新评分和等级
                const avgPollution = (airQualityData.pm25 + airQualityData.formaldehyde) / 2;
                if (avgPollution < 25) {
                    airQualityData.score = Math.floor(Math.random() * 10) + 20;
                    airQualityData.level = '空气优';
                } else if (avgPollution < 40) {
                    airQualityData.score = Math.floor(Math.random() * 15) + 35;
                    airQualityData.level = '空气良';
                } else {
                    airQualityData.score = Math.floor(Math.random() * 20) + 50;
                    airQualityData.level = '轻度污染';
                }

                // 模拟室外天气数据变化
                weatherData.temperature += (Math.random() - 0.5) * 0.5;
                weatherData.pm25Outdoor += Math.floor(Math.random() * 5) - 2;
                weatherData.pm6Outdoor += Math.floor(Math.random() * 3) - 1;
                weatherData.weatherIndex += Math.floor(Math.random() * 10) - 5;

                // 确保天气数据在合理范围
                weatherData.temperature = Math.max(25, Math.min(40, weatherData.temperature));
                weatherData.pm25Outdoor = Math.max(50, Math.min(200, weatherData.pm25Outdoor));
                weatherData.pm6Outdoor = Math.max(5, Math.min(50, weatherData.pm6Outdoor));
                weatherData.weatherIndex = Math.max(500, Math.min(800, weatherData.weatherIndex));

                // 更新显示
                updateAirQualityData();
                updateWeatherData();
                updateArcProgress();

            }, 3000); // 每3秒更新一次
        }
    </script>
</body>
</html>
