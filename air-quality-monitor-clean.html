<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车载空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'HarmonyOS Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* 响应式设计 */
        @media (max-width: 450px) {
            body {
                padding: 10px;
            }

            .phone-container {
                transform: scale(0.9);
            }
        }

        @media (max-width: 400px) {
            .phone-container {
                transform: scale(0.8);
            }
        }

        /* 可访问性改进 */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background-color: #f5f5f5;
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            position: absolute;
            top: 0;
            left: 11px;
            width: 375px;
            height: 44px;
        }

        .main-content {
            position: absolute;
            top: 69px;
            left: 0;
            width: 393px;
            height: 783px;
        }

        .background-image {
            position: absolute;
            top: 164px;
            left: 0;
            width: 393px;
            height: 619px;
            object-fit: cover;
        }

        .circles-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
        }

        .circle-outer {
            position: absolute;
            top: 36px;
            left: 40px;
            width: 311px;
            height: 274px;
        }

        .circle-middle {
            position: absolute;
            top: 78px;
            left: 75px;
            width: 231px;
            height: 197px;
        }

        .circle-inner {
            position: absolute;
            top: 119px;
            left: 121px;
            width: 175px;
            height: 136px;
        }

        .car-image {
            position: absolute;
            top: 69px;
            left: 39px;
            width: 315px;
            height: 210px;
            object-fit: cover;
        }

        .air-quality-indicator {
            position: absolute;
            top: 49px;
            left: 166px;
            width: 75px;
            height: 96px;
            text-align: center;
        }

        .air-quality-main {
            position: relative;
            margin-top: 9px;
            width: 69px;
            height: 87px;
        }

        .air-quality-title {
            color: #454545;
            font-size: 20px;
            font-weight: normal;
            margin-bottom: 5px;
            margin-left: 4px;
        }

        .air-quality-score {
            color: #1b705f;
            font-size: 60px;
            font-weight: normal;
            line-height: 1;
        }

        .air-quality-subtitle {
            position: absolute;
            top: 0;
            left: 10px;
            color: #454545;
            font-size: 6px;
            font-weight: normal;
            white-space: nowrap;
        }

        .air-quality-logo {
            position: absolute;
            top: 58px;
            left: 105px;
            width: 177px;
            height: 90px;
        }

        .mask-overlay {
            position: absolute;
            top: 0;
            left: 22px;
            width: 349px;
            height: 349px;
        }

        .health-protection {
            position: absolute;
            top: 256px;
            left: 109px;
            font-size: 15px;
            color: #5b5b5b;
        }

        .health-days {
            font-size: 19px;
            color: #5b5b5b;
        }

        .cleaning-reminder {
            position: absolute;
            top: 282px;
            left: 149px;
            font-size: 15px;
            white-space: nowrap;
        }

        .cleaning-text {
            color: #5b5b5b;
        }

        .cleaning-link {
            color: #ff8800;
        }

        .metrics-container {
            position: absolute;
            top: 320px;
            left: 42px;
            width: 298px;
            height: 45px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .metric-item {
            height: 45px;
            position: relative;
            text-align: left;
        }

        .metric-pm25 {
            width: 73px;
        }

        .metric-formaldehyde {
            width: 65px;
        }

        .metric-anion {
            width: 88px;
        }

        .metric-value {
            font-size: 28px;
            color: #494949;
            font-weight: normal;
            line-height: 1;
            margin-bottom: 5px;
            padding-left: 6px;
        }

        .metric-label {
            font-size: 10px;
            color: #909090;
            font-weight: normal;
            white-space: nowrap;
            position: absolute;
            bottom: 0;
            left: 0;
        }

        .weather-card {
            position: absolute;
            top: 409px;
            left: 23px;
            width: 345px;
            height: 183px;
        }

        .control-panel {
            position: absolute;
            top: 637px;
            left: 22px;
            width: 345px;
            height: 74px;
        }

        .card-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- Status bar -->
        <img class="status-bar" src="assets/images/status-bar.svg" alt="状态栏">

        <div class="main-content">

            <!-- Circular gradient elements -->
            <div class="circles-container">
                <img class="circle-outer" src="assets/images/ellipse-11.svg" alt="外圆">
                <img class="circle-middle" src="assets/images/ellipse-12.svg" alt="中圆">
                <img class="circle-inner" src="assets/images/ellipse-13.svg" alt="内圆">

                <!-- Car visualization -->
                <img class="car-image" src="assets/images/car.png" alt="车辆可视化">

                <!-- Air quality indicator -->
                <section class="air-quality-indicator" aria-label="空气质量指标">
                    <div class="air-quality-main">
                        <div class="air-quality-title" aria-label="空气质量等级">空气优</div>
                        <div class="air-quality-score" aria-label="空气质量评分">25</div>
                    </div>
                    <div class="air-quality-subtitle">车内综合空气质量</div>
                    <span class="sr-only">当前车内空气质量评级为优，评分25分</span>
                </section>

                <!-- Air quality logo -->
                <img class="air-quality-logo" src="assets/images/group-13.png" alt="空气质量标志">

                <!-- Mask group overlay -->
                
            </div>

            <!-- Health protection days -->
            <div class="health-protection">
                已为您健康守护 <span class="health-days">231</span> 天
            </div>

            <!-- Cleaning reminder setting -->
            <div class="cleaning-reminder">
                <span class="cleaning-text">设置 </span>
                <span class="cleaning-link">清洗提醒</span>
            </div>

            <!-- Air quality metrics -->
            <section class="metrics-container" aria-label="空气质量指标">
                <!-- PM2.5 -->
                <article class="metric-item metric-pm25">
                    <div class="metric-value">014</div>
                    <div class="metric-label">PM2.5(µg/m³）</div>
                </article>

                <!-- 甲醛 -->
                <article class="metric-item metric-formaldehyde">
                    <div class="metric-value">36</div>
                    <div class="metric-label">甲醛(µg/m³）</div>
                </article>

                <!-- 负氧离子 -->
                <article class="metric-item metric-anion">
                    <div class="metric-value">25500</div>
                    <div class="metric-label">负氧离子(个/cm³）</div>
                </article>
            </section>

            <!-- Weather and air quality card -->
            <div class="weather-card">
               
            </div>

            <!-- Control panel -->
            <div class="control-panel">
                
            </div>
        </div>
    </div>
</body>
</html>
