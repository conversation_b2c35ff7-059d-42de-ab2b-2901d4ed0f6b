<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车载空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'HarmonyOS Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* 响应式设计 */
        @media (max-width: 450px) {
            body {
                padding: 10px;
            }

            .phone-container {
                transform: scale(0.9);
            }
        }

        @media (max-width: 400px) {
            .phone-container {
                transform: scale(0.8);
            }
        }

        /* 可访问性改进 */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background-color: #f5f5f5;
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            position: absolute;
            top: 0;
            left: 11px;
            width: 375px;
            height: 44px;
        }

        .main-content {
            position: absolute;
            top: 69px;
            left: 0;
            width: 393px;
            height: 783px;
        }

        .background-image {
            position: absolute;
            top: 164px;
            left: 0;
            width: 393px;
            height: 619px;
            object-fit: cover;
        }

        .circles-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
        }

        .circle-outer {
            position: absolute;
            top: 36px;
            left: 40px;
            width: 311px;
            height: 274px;
        }

        .circle-middle {
            position: absolute;
            top: 78px;
            left: 75px;
            width: 231px;
            height: 197px;
        }

        .circle-inner {
            position: absolute;
            top: 119px;
            left: 121px;
            width: 175px;
            height: 136px;
        }

        .car-image {
            position: absolute;
            top: 69px;
            left: 39px;
            width: 315px;
            height: 210px;
            object-fit: cover;
        }

        .air-quality-indicator {
            position: absolute;
            top: 49px;
            left: 166px;
            width: 75px;
            height: 96px;
            text-align: center;
        }

        .air-quality-main {
            position: relative;
            margin-top: 9px;
            width: 69px;
            height: 87px;
        }

        .air-quality-title {
            color: #454545;
            font-size: 20px;
            font-weight: normal;
            margin-bottom: 5px;
            margin-left: 4px;
        }

        .air-quality-score {
            color: #1b705f;
            font-size: 60px;
            font-weight: normal;
            line-height: 1;
        }

        .air-quality-subtitle {
            position: absolute;
            top: 0;
            left: 10px;
            color: #454545;
            font-size: 6px;
            font-weight: normal;
            white-space: nowrap;
        }

        .air-quality-logo {
            position: absolute;
            top: 58px;
            left: 105px;
            width: 177px;
            height: 90px;
        }

        .mask-overlay {
            position: absolute;
            top: 0;
            left: 22px;
            width: 349px;
            height: 349px;
        }

        .health-protection {
            position: absolute;
            top: 256px;
            left: 109px;
            font-size: 15px;
            color: #5b5b5b;
        }

        .health-days {
            font-size: 19px;
            color: #5b5b5b;
        }

        .cleaning-reminder {
            position: absolute;
            top: 282px;
            left: 149px;
            font-size: 15px;
            white-space: nowrap;
        }

        .cleaning-text {
            color: #5b5b5b;
        }

        .cleaning-link {
            color: #ff8800;
        }

        .metrics-container {
            position: absolute;
            top: 320px;
            left: 42px;
            width: 298px;
            height: 45px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .metric-item {
            height: 45px;
            position: relative;
            text-align: left;
        }

        .metric-pm25 {
            width: 73px;
        }

        .metric-formaldehyde {
            width: 65px;
        }

        .metric-anion {
            width: 88px;
        }

        .metric-value {
            font-size: 28px;
            color: #494949;
            font-weight: normal;
            line-height: 1;
            margin-bottom: 5px;
            padding-left: 6px;
        }

        .metric-label {
            font-size: 10px;
            color: #909090;
            font-weight: normal;
            white-space: nowrap;
            position: absolute;
            bottom: 0;
            left: 0;
        }

        .weather-card {
            position: absolute;
            top: 409px;
            left: 23px;
            width: 345px;
            height: 183px;
        }

        .control-panel {
            position: absolute;
            top: 637px;
            left: 22px;
            width: 345px;
            height: 74px;
        }

        .card-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 动态背景 */
        .dynamic-background {
            position: absolute;
            top: 164px;
            left: 0;
            width: 393px;
            height: 619px;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
            animation: backgroundShift 8s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% {
                background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
            }
            50% {
                background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 50%, #ce93d8 100%);
            }
        }

        /* 动态遮罩效果 */
        .dynamic-mask {
            position: absolute;
            top: 0;
            left: 22px;
            width: 349px;
            height: 349px;
            background: radial-gradient(circle at center,
                rgba(255,255,255,0.1) 0%,
                rgba(255,255,255,0.05) 40%,
                transparent 70%);
            animation: maskPulse 4s ease-in-out infinite;
        }

        @keyframes maskPulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.6;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
        }

        /* 天气卡片动态内容 */
        .weather-content {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .weather-location {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .weather-temp {
            font-size: 24px;
            color: #333;
            font-weight: bold;
        }

        .weather-condition {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }

        .weather-details {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            color: #666;
        }

        .weather-item {
            text-align: center;
        }

        .weather-value {
            font-weight: bold;
            color: #333;
            display: block;
            margin-bottom: 2px;
        }

        /* 控制面板动态内容 */
        .control-content {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border-radius: 12px;
            padding: 12px 15px;
            height: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-button {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .control-button:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-1px);
        }

        .control-button.active {
            background: #3498db;
            border-color: #3498db;
        }

        /* 数值动画 */
        .animated-value {
            transition: all 0.5s ease;
        }

        .metric-value.updating {
            color: #3498db;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- Status bar -->
        <img class="status-bar" src="assets/images/status-bar.svg" alt="状态栏">

        <div class="main-content">
            <!-- 动态背景 -->
            <div class="dynamic-background"></div>

            <!-- Circular gradient elements -->
            <div class="circles-container">
                <img class="circle-outer" src="assets/images/ellipse-11.svg" alt="外圆">
                <img class="circle-middle" src="assets/images/ellipse-12.svg" alt="中圆">
                <img class="circle-inner" src="assets/images/ellipse-13.svg" alt="内圆">

                <!-- Car visualization -->
                <img class="car-image" src="assets/images/car.png" alt="车辆可视化">

                <!-- Air quality indicator -->
                <section class="air-quality-indicator" aria-label="空气质量指标">
                    <div class="air-quality-main">
                        <div class="air-quality-title" aria-label="空气质量等级">空气优</div>
                        <div class="air-quality-score" aria-label="空气质量评分">25</div>
                    </div>
                    <div class="air-quality-subtitle">车内综合空气质量</div>
                    <span class="sr-only">当前车内空气质量评级为优，评分25分</span>
                </section>

                <!-- Air quality logo -->
                <img class="air-quality-logo" src="assets/images/group-13.png" alt="空气质量标志">

                <!-- 动态遮罩效果 -->
                <div class="dynamic-mask"></div>
            </div>

            <!-- Health protection days -->
            <div class="health-protection">
                已为您健康守护 <span class="health-days">231</span> 天
            </div>

            <!-- Cleaning reminder setting -->
            <div class="cleaning-reminder">
                <span class="cleaning-text">设置 </span>
                <span class="cleaning-link">清洗提醒</span>
            </div>

            <!-- Air quality metrics -->
            <section class="metrics-container" aria-label="空气质量指标">
                <!-- PM2.5 -->
                <article class="metric-item metric-pm25">
                    <div class="metric-value">014</div>
                    <div class="metric-label">PM2.5(µg/m³）</div>
                </article>

                <!-- 甲醛 -->
                <article class="metric-item metric-formaldehyde">
                    <div class="metric-value">36</div>
                    <div class="metric-label">甲醛(µg/m³）</div>
                </article>

                <!-- 负氧离子 -->
                <article class="metric-item metric-anion">
                    <div class="metric-value">25500</div>
                    <div class="metric-label">负氧离子(个/cm³）</div>
                </article>
            </section>

            <!-- Weather and air quality card -->
            <div class="weather-card">
                <div class="weather-content">
                    <div class="weather-header">
                        <div>
                            <div class="weather-location">北京市</div>
                            <div class="weather-condition" id="weather-condition">晴转多云</div>
                        </div>
                        <div class="weather-temp" id="temperature">22°C</div>
                    </div>
                    <div class="weather-details">
                        <div class="weather-item">
                            <span class="weather-value" id="humidity">65%</span>
                            <span>湿度</span>
                        </div>
                        <div class="weather-item">
                            <span class="weather-value" id="wind-speed">3.2m/s</span>
                            <span>风速</span>
                        </div>
                        <div class="weather-item">
                            <span class="weather-value" id="pressure">1013</span>
                            <span>气压</span>
                        </div>
                        <div class="weather-item">
                            <span class="weather-value" id="uv-index">中等</span>
                            <span>紫外线</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Control panel -->
            <div class="control-panel">
                <div class="control-content">
                    <button class="control-button" id="air-purifier" onclick="toggleControl('air-purifier')">
                        空气净化
                    </button>
                    <button class="control-button" id="circulation" onclick="toggleControl('circulation')">
                        内循环
                    </button>
                    <button class="control-button active" id="auto-mode" onclick="toggleControl('auto-mode')">
                        自动模式
                    </button>
                    <button class="control-button" id="settings" onclick="openSettings()">
                        设置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 空气质量数据模拟
        const airQualityData = {
            score: 25,
            level: '空气优',
            pm25: 14,
            formaldehyde: 36,
            anion: 25500,
            protectionDays: 231
        };

        // 天气数据模拟
        const weatherData = {
            temperature: 22,
            condition: '晴转多云',
            humidity: 65,
            windSpeed: 3.2,
            pressure: 1013,
            uvIndex: '中等'
        };

        // 控制面板状态
        const controlStates = {
            'air-purifier': false,
            'circulation': false,
            'auto-mode': true,
            'settings': false
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateAirQualityData();
            updateWeatherData();
            startDataSimulation();
        });

        // 更新空气质量数据
        function updateAirQualityData() {
            // 添加更新动画
            const scoreElement = document.querySelector('.air-quality-score');
            const titleElement = document.querySelector('.air-quality-title');
            const pm25Element = document.querySelector('.metric-pm25 .metric-value');
            const formaldehydeElement = document.querySelector('.metric-formaldehyde .metric-value');
            const anionElement = document.querySelector('.metric-anion .metric-value');

            // 动画效果
            [scoreElement, titleElement, pm25Element, formaldehydeElement, anionElement].forEach(el => {
                if (el) {
                    el.classList.add('updating');
                    setTimeout(() => el.classList.remove('updating'), 500);
                }
            });

            // 更新数值
            if (scoreElement) scoreElement.textContent = airQualityData.score;
            if (titleElement) titleElement.textContent = airQualityData.level;
            if (pm25Element) pm25Element.textContent = airQualityData.pm25.toString().padStart(3, '0');
            if (formaldehydeElement) formaldehydeElement.textContent = airQualityData.formaldehyde;
            if (anionElement) anionElement.textContent = airQualityData.anion;

            // 更新保护天数
            const daysElement = document.querySelector('.health-days');
            if (daysElement) daysElement.textContent = airQualityData.protectionDays;

            // 根据空气质量更新颜色
            if (scoreElement) {
                if (airQualityData.score <= 30) {
                    scoreElement.style.color = '#1b705f'; // 优 - 绿色
                } else if (airQualityData.score <= 50) {
                    scoreElement.style.color = '#f39c12'; // 良 - 橙色
                } else {
                    scoreElement.style.color = '#e74c3c'; // 污染 - 红色
                }
            }
        }

        // 更新天气数据
        function updateWeatherData() {
            const elements = {
                temperature: document.getElementById('temperature'),
                condition: document.getElementById('weather-condition'),
                humidity: document.getElementById('humidity'),
                windSpeed: document.getElementById('wind-speed'),
                pressure: document.getElementById('pressure'),
                uvIndex: document.getElementById('uv-index')
            };

            if (elements.temperature) elements.temperature.textContent = `${weatherData.temperature}°C`;
            if (elements.condition) elements.condition.textContent = weatherData.condition;
            if (elements.humidity) elements.humidity.textContent = `${weatherData.humidity}%`;
            if (elements.windSpeed) elements.windSpeed.textContent = `${weatherData.windSpeed}m/s`;
            if (elements.pressure) elements.pressure.textContent = weatherData.pressure;
            if (elements.uvIndex) elements.uvIndex.textContent = weatherData.uvIndex;
        }

        // 控制面板切换
        function toggleControl(controlId) {
            const button = document.getElementById(controlId);
            if (!button) return;

            // 切换状态
            controlStates[controlId] = !controlStates[controlId];

            // 更新按钮样式
            if (controlStates[controlId]) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }

            // 模拟控制效果
            console.log(`${controlId} ${controlStates[controlId] ? '开启' : '关闭'}`);

            // 如果开启空气净化，模拟数据改善
            if (controlId === 'air-purifier' && controlStates[controlId]) {
                setTimeout(() => {
                    airQualityData.pm25 = Math.max(5, airQualityData.pm25 - 2);
                    airQualityData.formaldehyde = Math.max(20, airQualityData.formaldehyde - 3);
                    updateAirQualityData();
                }, 2000);
            }
        }

        // 设置按钮
        function openSettings() {
            alert('设置功能开发中...\n\n可配置项：\n- 空气质量阈值\n- 自动净化模式\n- 数据更新频率\n- 提醒设置');
        }

        // 数据模拟更新
        function startDataSimulation() {
            setInterval(() => {
                // 模拟数据微小变化
                airQualityData.pm25 += Math.floor(Math.random() * 3) - 1; // -1 到 1
                airQualityData.formaldehyde += Math.floor(Math.random() * 3) - 1;
                airQualityData.anion += Math.floor(Math.random() * 200) - 100; // -100 到 100

                // 确保数据在合理范围内
                airQualityData.pm25 = Math.max(5, Math.min(50, airQualityData.pm25));
                airQualityData.formaldehyde = Math.max(20, Math.min(80, airQualityData.formaldehyde));
                airQualityData.anion = Math.max(20000, Math.min(30000, airQualityData.anion));

                // 更新评分
                const avgPollution = (airQualityData.pm25 + airQualityData.formaldehyde) / 2;
                if (avgPollution < 20) {
                    airQualityData.score = Math.floor(Math.random() * 10) + 20; // 20-30
                    airQualityData.level = '空气优';
                } else if (avgPollution < 35) {
                    airQualityData.score = Math.floor(Math.random() * 15) + 35; // 35-50
                    airQualityData.level = '空气良';
                } else {
                    airQualityData.score = Math.floor(Math.random() * 20) + 50; // 50-70
                    airQualityData.level = '轻度污染';
                }

                // 更新天气数据
                weatherData.temperature += (Math.random() - 0.5) * 0.5; // 小幅度变化
                weatherData.humidity += Math.floor(Math.random() * 3) - 1;
                weatherData.windSpeed += (Math.random() - 0.5) * 0.2;
                weatherData.pressure += Math.floor(Math.random() * 3) - 1;

                // 确保天气数据在合理范围
                weatherData.temperature = Math.max(15, Math.min(35, weatherData.temperature));
                weatherData.humidity = Math.max(30, Math.min(90, weatherData.humidity));
                weatherData.windSpeed = Math.max(0.5, Math.min(8.0, weatherData.windSpeed));
                weatherData.pressure = Math.max(1000, Math.min(1030, weatherData.pressure));

                // 更新显示
                updateAirQualityData();
                updateWeatherData();

            }, 5000); // 每5秒更新一次
        }

        // 清洗提醒点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const cleaningReminder = document.querySelector('.cleaning-link');
            if (cleaningReminder) {
                cleaningReminder.addEventListener('click', function() {
                    alert('清洗提醒设置\n\n当前设置：每30天提醒一次\n上次清洗：15天前\n下次提醒：15天后');
                });
                cleaningReminder.style.cursor = 'pointer';
            }
        });
    </script>
</body>
</html>
