# @animaapp/vite-plugin-screen-graph

A Vite plugin that generates a graph of screen navigation in React applications.

## Installation

```bash
npm install --save-dev @animaapp/vite-plugin-screen-graph
# or
yarn add --dev @animaapp/vite-plugin-screen-graph
# or
pnpm add -D @animaapp/vite-plugin-screen-graph
```

## Usage

Add the plugin to your `vite.config.ts` file:

```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { screenGraphPlugin } from '@animaapp/vite-plugin-screen-graph';

export default defineConfig({
  plugins: [
    react(),
    screenGraphPlugin()
  ],
});
```

When you build your project, the plugin will analyze your React components and generate a `.screen-graph.json` file in the root of your project. This file contains a graph representation of your application's screen navigation.

## How it works

The plugin analyzes your React components and identifies navigation between screens by looking for:

- `<Link>` and `<NavLink>` components from React Router
- `<Navigate>` components from React Router
- `<a>` tags with `href` attributes
- `navigate()` function calls from React Router

It then builds a graph where:
- Nodes represent screens (React components associated with routes)
- Edges represent navigation paths between screens

## Output format

The generated `.screen-graph.json` file has the following structure:

```json
{
  "nodes": [
    {
      "id": "path/to/HomeScreen.tsx",
      "label": "HomeScreen",
      "isRoot": true
    },
    {
      "id": "path/to/ProfileScreen.tsx",
      "label": "ProfileScreen"
    },
    // ...
  ],
  "edges": [
    {
      "id": "path/to/HomeScreen.tsx:42:10-to-path/to/ProfileScreen.tsx",
      "source": "path/to/HomeScreen.tsx",
      "target": "path/to/ProfileScreen.tsx",
      "data": {
        "viaRoute": "/profile",
        "trigger": {
          "element": "<Link to=\"/profile\">Go to Profile</Link>",
          "line": 42,
          "column": 10,
          "sourceFile": "path/to/HomeScreen.tsx"
        }
      }
    },
    // ...
  ]
}
```

### Node Structure

- `id`: The full file path to the component, ensuring uniqueness even if multiple components have the same name
- `label`: The component name (basename of the file without extension) for display purposes
- `isRoot`: A boolean flag (only present on the root screen) indicating if this is the root screen (path: "/")

### Edge Structure

- `id`: A unique identifier combining the source location (file, line, column) and target file
- `source`: The source node ID (file path)
- `target`: The target node ID (file path)
- `data`: Additional information about the navigation
  - `viaRoute`: The route path used for navigation
  - `trigger`: Information about what triggered the navigation
    - `element`: The full element snippet that triggered the navigation (e.g., `<Link to="/profile">Go to Profile</Link>`)
    - `line`: The line number in the source file
    - `column`: The column number in the source file
    - `sourceFile`: The source file path

## License

MIT
