import N from"path";import E from"fs/promises";import A from"fast-glob";import{parse as b}from"@babel/parser";import{walk as F}from"estree-walker";import P from"path";function x(e){let r=process.cwd();return P.relative(r,e)}function J(e,r,c,m){return`${x(e)}:${r}:${c}-to-${x(m)}`}var w=(e,{code:r,filePath:c,parentScreen:m})=>{let p=[];if(e.type==="JSXElement"&&e.openingElement.name.type==="JSXIdentifier"){let g=e.openingElement.name.name;if(["Link","NavLink","Navigate","a"].includes(g))for(let f of e.openingElement.attributes)f.type==="JSXAttribute"&&(g==="a"&&f.name.name==="href"||g!=="a"&&f.name.name==="to")&&f.value?.type==="StringLiteral"&&p.push({path:f.value.value,line:e.loc?.start.line??0,endLine:e.loc?.end.line??0,column:e.loc?.start.column??0,endColumn:e.loc?.end.column??0,element:r.substring(e.start,e.end),sourceFile:c,parentScreen:m})}return p};var X=(e,{code:r,filePath:c,parentScreen:m})=>{let p=[];return e.type==="CallExpression"&&e.callee.type==="Identifier"&&e.callee.name==="navigate"&&e.arguments[0]?.type==="StringLiteral"&&p.push({path:e.arguments[0].value,line:e.loc?.start.line??0,endLine:e.loc?.end.line??0,column:e.loc?.start.column??0,endColumn:e.loc?.end.column??0,element:r.substring(e.start,e.end),sourceFile:c,parentScreen:m}),p};var C=(e,{code:r,filePath:c,parentScreen:m,routeMap:p})=>{let g=[];if(e.type==="JSXElement"&&e.openingElement.name.type==="JSXIdentifier"){for(let f of e.openingElement.attributes)if(f.type==="JSXAttribute"&&f.value?.type==="StringLiteral"){let y=f.value.value;p.has(y)&&g.push({path:y,line:e.loc?.start.line??0,endLine:e.loc?.end.line??0,column:e.loc?.start.column??0,endColumn:e.loc?.end.column??0,element:r.substring(e.start,e.end),sourceFile:c,parentScreen:m})}}return g};var j=[w,C,X],S=new Map,L=new Map;async function T(e,r){let c=N.resolve(N.dirname(r),e);for(let m of[".tsx",".jsx",".ts",".js"]){let p=`${c}${m}`;try{return await E.access(p),p}catch{}}for(let m of[".tsx",".jsx",".ts",".js"]){let p=N.join(c,`index${m}`);try{return await E.access(p),p}catch{}}return null}async function I(e,r,c,m=new Set,p){if(S.has(e))return S.get(e);if(m.has(e))return{hasNavigation:!1,navigationTargets:[]};m.add(e);let g={hasNavigation:!1,navigationTargets:[]};try{let f=await E.readFile(e,"utf8"),y=b(f,{sourceType:"module",plugins:["typescript","jsx"]}),v=[];F(y,{enter(i){if(i.type==="ImportDeclaration"){let t=i.source.value;if(t.startsWith("./")||t.startsWith("../"))for(let s of i.specifiers)v.push({name:s.local.name,path:t,isDefault:s.type==="ImportDefaultSpecifier",isReExport:!1})}if(i.type==="ExportNamedDeclaration"&&i.source?.type==="StringLiteral"){let t=i.source.value;for(let s of i.specifiers||[])v.push({name:s.exported.name,path:t,isDefault:!1,isReExport:!0})}}}),L.set(e,v);let u=[];F(y,{enter(i){for(let t of j){let n=t(i,{code:f,filePath:e,parentScreen:p,routeMap:r}).filter(a=>!u.some(o=>o.sourceFile===a.sourceFile&&o.line===a.line&&o.column===a.column&&o.path===a.path));u.push(...n)}}});let h=[];for(let i of v)i.isReExport&&h.push(T(i.path,e).then(async t=>t?I(t,r,c,m,p||e):{hasNavigation:!1,navigationTargets:[]}));F(y,{enter(i){if(i.type==="JSXElement"&&i.openingElement.name.type==="JSXIdentifier"){let t=i.openingElement.name.name,s=v.find(n=>n.name===t&&!n.isReExport);s&&h.push(T(s.path,e).then(async n=>n?I(n,r,c,m,p||e):{hasNavigation:!1,navigationTargets:[]}))}}});let l=await Promise.all(h);g.navigationTargets=u;for(let i of l)i.hasNavigation&&(g.hasNavigation=!0,g.navigationTargets.push(...i.navigationTargets));u.length>0&&(g.hasNavigation=!0)}catch(f){console.warn(`Failed to analyze component file: ${e}`,f)}return S.set(e,g),g}function Y(){return{name:"vite-plugin-screen-graph",async buildStart(){let e=await A("src/**/*.{tsx,jsx}"),r=new Map,c=new Map;for(let y of e){let v=await E.readFile(y,"utf8"),u=b(v,{sourceType:"module",plugins:["typescript","jsx"]}),h=new Map,l="";F(u,{enter(t){if(t.type==="ImportDeclaration"){let s=t.source.value;if(s.startsWith("./")||s.startsWith("../")){for(let n of t.specifiers)if(n.type==="ImportDefaultSpecifier"){let a=n.local.name,o=N.resolve(N.dirname(y),s),d=N.basename(o,N.extname(o));h.set(a,d)}}}t.type==="ExportDefaultDeclaration"&&t.declaration.type==="FunctionDeclaration"&&t.declaration.id&&(l=t.declaration.id.name)}}),F(u,{enter(t){if(t.type==="JSXElement"&&t.openingElement.name.type==="JSXIdentifier"&&t.openingElement.name.name==="Route"){let s="",n="";for(let a of t.openingElement.attributes)if(a.type==="JSXAttribute"&&a.name.name==="path"&&a.value?.type==="StringLiteral"&&(s=a.value.value),a.type==="JSXAttribute"&&a.name.name==="element"){let o=a.value;if(o?.type==="JSXExpressionContainer"&&o.expression.type==="JSXElement"){let d=o.expression.openingElement.name;d.type==="JSXIdentifier"&&(n=d.name)}}if(s&&n){let a=h.get(n);if(a)r.set(s,a);else if(n===l){let o=N.basename(y,N.extname(y));r.set(s,o)}else r.set(s,n)}}if(t.type==="CallExpression"&&t.callee.type==="Identifier"&&t.callee.name==="createBrowserRouter"&&t.arguments[0]?.type==="ArrayExpression"){for(let s of t.arguments[0].elements)if(s?.type==="ObjectExpression"){let n="",a="";for(let o of s.properties)if(o.type==="ObjectProperty"&&o.key.type==="Identifier"){if(o.key.name==="path"&&o.value.type==="StringLiteral")n=o.value.value;else if(o.key.name==="element"&&o.value.type==="JSXElement"){let d=o.value.openingElement.name;d.type==="JSXIdentifier"&&(a=d.name)}}n&&a&&r.set(n,a)}}}});let i=N.basename(y,N.extname(y));c.set(i,y)}let m=new Map,p=[];for(let[y,v]of r.entries()){let u=c.get(v);if(u){let h=await E.readFile(u,"utf8"),l=b(h,{sourceType:"module",plugins:["typescript","jsx"]}),i;F(l,{enter(n){if(n.type==="JSXElement"&&n.openingElement.name.type==="JSXIdentifier"&&n.openingElement.name.name!=="Fragment"){for(let a of n.openingElement.attributes)a.type==="JSXAttribute"&&a.name.name==="data-model-id"&&a.value?.type==="StringLiteral"&&(i=a.value.value);this.skip()}}});let t=Array.from(r.entries()).filter(([,n])=>n===v).map(([n])=>n),s=t.includes("/")||t.includes("/*");m.set(u,{id:u,label:v,routes:t,...i&&{dataModelId:i},...s&&{isRoot:!0}})}}let g=new Set;for(let[y,v]of r.entries()){let u=c.get(v);if(u&&!g.has(u)){g.add(u);let h=await I(u,r,c);if(h.hasNavigation)for(let l of h.navigationTargets){let i=r.get(l.path);if(i){let t=c.get(i);t&&p.push({id:J(l.sourceFile,l.line,l.column,t),source:l.parentScreen?x(l.parentScreen):x(l.sourceFile),target:x(t),data:{viaRoute:l.path,trigger:{element:l.element,line:l.line,endLine:l.endLine,column:l.column,endColumn:l.endColumn,sourceFile:x(l.sourceFile)}}})}}}}let f={nodes:Array.from(m.values()),edges:p};await E.writeFile(".screen-graph.json",JSON.stringify(f,null,2)),console.log("[vite-plugin-screen-graph] \u2705 Generated .screen-graph.json")}}}export{Y as screenGraphPlugin};
