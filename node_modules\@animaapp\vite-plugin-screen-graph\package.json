{"name": "@animaapp/vite-plugin-screen-graph", "version": "0.1.11", "description": "Vite plugin that generates a graph of screen navigation in React applications", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "author": "Anima App", "repository": {"type": "git", "url": "https://github.com/AnimaApp/vite-plugin-screen-graph"}, "homepage": "https://github.com/AnimaApp/vite-plugin-screen-graph#readme", "bugs": {"url": "https://github.com/AnimaApp/vite-plugin-screen-graph/issues"}, "keywords": ["vite-plugin", "vite", "react", "navigation", "screen-graph", "visualization"], "scripts": {"build": "tsup src/index.ts --format esm --dts --clean --minify", "test": "tsx test/run-tests.ts", "clean": "rm -rf dist && rm -rf node_modules && pnpm clean:test", "clean:test": "rm -rf test/projects/*/dist && rm -rf test/projects/*/node_modules && rm -rf test/projects/*/.screen-graph.json", "deploy": "pnpm build && npm publish --access=public"}, "publishConfig": {"access": "public"}, "devDependencies": {"@types/babel__traverse": "^7.20.5", "@types/estree": "^1.0.7", "@types/node": "^22.14.0", "@vitejs/plugin-react": "^4.3.4", "ts-node": "^10.9.2", "tsup": "^8.4.0", "tsx": "^4.19.3", "typescript": "^5.3.3"}, "packageManager": "pnpm@9.14.4+sha512.c8180b3fbe4e4bca02c94234717896b5529740a6cbadf19fa78254270403ea2f27d4e1d46a08a0f56c89b63dc8ebfd3ee53326da720273794e6200fcf0d184ab", "dependencies": {"@babel/parser": "^7.24.1", "@babel/traverse": "^7.24.1", "@babel/types": "^7.27.0", "estree-walker": "^3.0.3", "fast-glob": "^3.2.12", "vite": "^5.0.0"}, "files": ["dist"]}