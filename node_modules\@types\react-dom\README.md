# Installation
> `npm install --save @types/react-dom`

# Summary
This package contains type definitions for React (react-dom) (https://reactjs.org).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-dom.

### Additional Details
 * Last updated: <PERSON><PERSON>, 25 Apr 2023 10:32:40 GMT
 * Dependencies: [@types/react](https://npmjs.com/package/@types/react)
 * Global values: `ReactDOM`, `ReactDOMServer`

# Credits
These definitions were written by [<PERSON><PERSON>](https://asana.com), [AssureSign](http://www.assuresign.com), [Microsoft](https://microsoft.com), [<PERSON><PERSON><PERSON><PERSON><PERSON>kas](https://github.com/<PERSON><PERSON>kas), [<PERSON>](https://github.com/theruther4d), [<PERSON>](https://github.com/<PERSON>id<PERSON>), and [<PERSON>](https://github.com/eps1lon).
